from tree_sitter_language_pack import get_language
from tree_sitter import Parser
from typing import List, Dict, Any, Optional
import os
from embedding_config import get_embedding_config, estimate_tokens
from semantic_chunking_enhancement import SemanticChunker, integrate_semantic_chunking

class TreeSitterChunker:
    def __init__(self, embedding_model: Optional[str] = None):
        # Embedding model configuration
        self.embedding_model = embedding_model or os.getenv("EMBEDDING_MODEL", "nomic-embed-text")
        self.embedding_config = get_embedding_config(self.embedding_model)

        # Language mapping from our system to Tree-sitter
        self.language_map = {
            'c_cpp': ['c', 'cpp'],  # C/C++ uses both parsers
            'python': ['python'],
            'javascript': ['javascript'],
            'typescript': ['typescript'],
            'rust': ['rust'],
            'go': ['go'],
            'java': ['java'],
            'csharp': ['csharp'],
            'php': ['php'],
            'ruby': ['ruby'],
            'bash': ['bash'],
            'html': ['html'],
            'css': ['css'],
            'json': ['json'],
            'yaml': ['yaml'],
            'sql': ['sql'],
            'markdown': ['markdown'],
            'lua': ['lua'],
            'perl': ['perl'],
            'scala': ['scala'],
            'kotlin': ['kotlin'],
            'swift': ['swift'],
            'haskell': ['haskell'],
            'elixir': ['elixir'],
            'erlang': ['erlang'],
            'clojure': ['clojure']
        }

        # Cache for loaded languages
        self.language_cache: Dict[str, Any] = {}

        # Semantic chunker for enhanced analysis
        self.semantic_chunker = SemanticChunker()



    def get_language(self, language_name: str, file_path: str = ""):
        """Get Tree-sitter language parser for given language"""
        if language_name not in self.language_map:
            return None

        # Special handling for C/C++
        if language_name == 'c_cpp':
            if file_path.endswith(('.cpp', '.cxx', '.cc', '.c++', '.hpp', '.hxx', '.hh')):
                ts_lang = 'cpp'
            else:
                ts_lang = 'c'
        else:
            ts_lang = self.language_map[language_name][0]

        # Cache the language
        if ts_lang not in self.language_cache:
            try:
                # Type ignore: ts_lang is guaranteed to be a valid language name from our mapping
                self.language_cache[ts_lang] = get_language(ts_lang)  # type: ignore
            except Exception as e:
                print(f"⚠️ Failed to load Tree-sitter language '{ts_lang}': {e}")
                return None

        return self.language_cache[ts_lang]

    def chunk_file(self, file_path: str, content: str, language: str) -> List[Dict[str, Any]]:
        """Universal file chunking using Tree-sitter for any supported language"""

        # Get Tree-sitter language
        ts_language = self.get_language(language, file_path)
        if not ts_language:
            # Fallback to simple file-level chunk
            return self._create_fallback_chunk(file_path, content)

        try:
            parser = Parser(ts_language)
            tree = parser.parse(bytes(content, 'utf8'))

            chunks = []

            # Find language-specific constructs
            constructs = self._find_language_constructs(tree.root_node, content, language)

            # Filter constructs to prevent over-chunking
            filtered_constructs = self._filter_meaningful_constructs(constructs)

            for i, construct in enumerate(filtered_constructs):
                chunk = {
                    'content': construct['text'],
                    'file_path': file_path,
                    'start_line': construct['start'][0] + 1,
                    'end_line': construct['end'][0] + 1,
                    'chunk_type': construct['type'],
                    'function_name': construct.get('name', f'{construct["type"]}_{i+1}')
                }
                chunks.append(chunk)

            # If no meaningful constructs found, return entire file as one chunk
            if not chunks:
                return self._create_fallback_chunk(file_path, content)

            print(f"🔍 Tree-sitter: Filtered {len(constructs)} -> {len(filtered_constructs)} meaningful chunks")
            return chunks

        except Exception as e:
            print(f"⚠️ Tree-sitter parsing failed for {file_path}: {e}")
            return self._create_fallback_chunk(file_path, content)

    def chunk_c_cpp_file(self, file_path: str, content: str) -> List[Dict[str, Any]]:
        """Legacy method for C/C++ - now uses universal chunker"""
        return self.chunk_file(file_path, content, 'c_cpp')

    def _create_fallback_chunk(self, file_path: str, content: str) -> List[Dict[str, Any]]:
        """Create fallback chunk when Tree-sitter parsing fails"""
        return [{
            'content': content,
            'file_path': file_path,
            'start_line': 1,
            'end_line': len(content.split('\n')),
            'chunk_type': 'file',
            'function_name': 'entire_file'
        }]

    def _find_language_constructs(self, node, content: str, language: str, constructs=None):
        """Find language-specific constructs (functions, classes, methods, etc.)"""
        if constructs is None:
            constructs = []

        # Define node types to look for by language (SELECTIVE to prevent over-chunking)
        target_nodes = {
            'c_cpp': ['function_definition'],
            'python': ['function_definition', 'class_definition'],
            'javascript': ['function_declaration', 'class_declaration'],  # Removed expressions/arrows
            'typescript': ['function_declaration', 'class_declaration', 'interface_declaration'],  # Removed expressions/arrows
            'java': ['method_declaration', 'class_declaration', 'interface_declaration'],
            'csharp': ['method_declaration', 'class_declaration', 'interface_declaration', 'struct_declaration'],
            'rust': ['function_item', 'impl_item', 'struct_item', 'enum_item', 'trait_item'],
            'go': ['function_declaration', 'method_declaration', 'type_declaration'],
            'php': ['function_definition', 'class_declaration', 'method_declaration'],
            'ruby': ['method', 'class', 'module'],
            'swift': ['function_declaration', 'class_declaration', 'struct_declaration', 'protocol_declaration'],
            'kotlin': ['function_declaration', 'class_declaration', 'object_declaration'],
            'scala': ['function_definition', 'class_definition', 'object_definition', 'trait_definition'],
            'haskell': ['function_declaration', 'data_declaration', 'type_declaration'],
            'elixir': ['function_definition', 'module'],
            'erlang': ['function_clause', 'module_attribute'],
            'clojure': ['defn', 'defmacro', 'defprotocol'],
            'lua': ['function_statement', 'local_function_statement'],
            'perl': ['subroutine_declaration'],
            'bash': ['function_definition'],
            'sql': ['create_table_statement', 'create_function_statement', 'create_procedure_statement'],
            'yaml': ['block_mapping'],  # Removed sequences to reduce chunks
            'json': ['object'],  # Removed arrays to reduce chunks
            'html': ['element'],  # Only major elements
            'css': ['rule_set'],  # Removed at_rule to reduce chunks
            'markdown': ['section']  # Removed code_block to reduce chunks
        }

        # Get target nodes for this language
        targets = target_nodes.get(language, ['function_definition'])  # Default to function_definition

        # Check if current node is a target
        if node.type in targets:
            construct_text = content[node.start_byte:node.end_byte]
            construct_name = self._extract_construct_name(node, content, language)
            construct_type = self._map_node_type_to_chunk_type(node.type)

            constructs.append({
                'start': node.start_point,
                'end': node.end_point,
                'text': construct_text,
                'name': construct_name,
                'type': construct_type
            })

        # Recursively search children
        for child in node.children:
            self._find_language_constructs(child, content, language, constructs)

        return constructs

    def _filter_meaningful_constructs(self, constructs: List[Dict]) -> List[Dict]:
        """Filter out tiny/meaningless chunks to prevent over-chunking with embedding model awareness"""

        # Get embedding model limits
        safe_chunk_size = self.embedding_config.safe_chunk_size

        # Configuration for meaningful chunks (embedding-aware)
        MIN_LINES = 3          # Minimum 3 lines
        MIN_CHARS = 50         # Minimum 50 characters
        MAX_TOKENS = safe_chunk_size  # Use safe chunk size from config
        MAX_CHUNKS_PER_FILE = 50  # Maximum chunks per file to prevent explosion

        print(f"🔧 Chunk filtering: max_tokens={MAX_TOKENS} (safe size for {self.embedding_model})")

        # Types to prioritize (keep even if small)
        PRIORITY_TYPES = {
            'function', 'method', 'class', 'interface', 'struct',
            'enum', 'trait', 'module', 'implementation'
        }

        # Types to avoid (too granular)
        AVOID_TYPES = {
            'expression', 'statement', 'literal', 'identifier',
            'comment', 'import', 'variable_declaration'
        }

        filtered = []

        for construct in constructs:
            # Skip avoided types
            if construct['type'] in AVOID_TYPES:
                continue

            # Calculate construct metrics
            start_line = construct['start'][0]
            end_line = construct['end'][0]
            line_count = end_line - start_line + 1
            char_count = len(construct['text'])
            token_count = estimate_tokens(construct['text'])
            construct_type = construct['type']

            # Skip chunks that exceed token limit
            if token_count > MAX_TOKENS:
                print(f"⚠️ Skipping large chunk: {token_count} tokens > {MAX_TOKENS} limit ({construct_type})")
                continue

            # Priority types get relaxed filtering
            if construct_type in PRIORITY_TYPES:
                if line_count >= 2 and char_count >= 30 and token_count <= MAX_TOKENS:  # Added token check
                    filtered.append(construct)
            else:
                # Regular filtering for other types
                if line_count >= MIN_LINES and char_count >= MIN_CHARS and token_count <= MAX_TOKENS:
                    filtered.append(construct)

        # If we have too many chunks, keep only the largest ones
        if len(filtered) > MAX_CHUNKS_PER_FILE:
            # Sort by size (line count * char count) and keep top chunks
            filtered.sort(key=lambda x: (x['end'][0] - x['start'][0] + 1) * len(x['text']), reverse=True)
            filtered = filtered[:MAX_CHUNKS_PER_FILE]
            print(f"⚠️ Too many chunks ({len(constructs)}), kept top {MAX_CHUNKS_PER_FILE} by size")

        return filtered

    def _extract_construct_name(self, node, content: str, language: str) -> str:
        """Extract name from various language constructs"""

        # Language-specific name extraction patterns
        name_patterns = {
            'c_cpp': ['function_declarator', 'identifier'],
            'python': ['identifier'],
            'javascript': ['identifier'],
            'typescript': ['identifier'],
            'java': ['identifier'],
            'csharp': ['identifier'],
            'rust': ['identifier'],
            'go': ['identifier'],
            'php': ['name'],
            'ruby': ['identifier', 'constant'],
            'swift': ['identifier'],
            'kotlin': ['simple_identifier'],
            'scala': ['identifier'],
            'haskell': ['variable'],
            'elixir': ['identifier'],
            'erlang': ['atom'],
            'clojure': ['symbol'],
            'lua': ['identifier'],
            'perl': ['identifier'],
            'bash': ['word']
        }

        patterns = name_patterns.get(language, ['identifier'])

        # Try to find name using patterns
        for pattern in patterns:
            name = self._find_name_by_pattern(node, content, pattern)
            if name:
                return name

        return 'unknown_construct'

    def _find_name_by_pattern(self, node, content: str, pattern: str) -> str:
        """Find name using specific pattern"""
        if node.type == pattern:
            return content[node.start_byte:node.end_byte]

        for child in node.children:
            if child.type == pattern:
                return content[child.start_byte:child.end_byte]
            # Recursive search for nested patterns
            result = self._find_name_by_pattern(child, content, pattern)
            if result:
                return result

        return ""

    def _map_node_type_to_chunk_type(self, node_type: str) -> str:
        """Map Tree-sitter node types to our chunk types"""
        type_mapping = {
            'function_definition': 'function',
            'function_declaration': 'function',
            'function_expression': 'function',
            'arrow_function': 'function',
            'method_declaration': 'method',
            'class_declaration': 'class',
            'class_definition': 'class',
            'interface_declaration': 'interface',
            'interface_definition': 'interface',
            'struct_declaration': 'struct',
            'struct_item': 'struct',
            'enum_item': 'enum',
            'trait_item': 'trait',
            'impl_item': 'implementation',
            'module': 'module',
            'object_declaration': 'object',
            'type_declaration': 'type',
            'data_declaration': 'data',
            'defn': 'function',
            'defmacro': 'macro',
            'defprotocol': 'protocol',
            'subroutine_declaration': 'subroutine',
            'create_table_statement': 'table',
            'create_function_statement': 'function',
            'create_procedure_statement': 'procedure',
            'rule_set': 'rule',
            'at_rule': 'at_rule',
            'element': 'element',
            'section': 'section',
            'code_block': 'code_block',
            'block_mapping': 'mapping',
            'block_sequence': 'sequence',
            'object': 'object',
            'array': 'array'
        }

        return type_mapping.get(node_type, 'construct')

    def _find_functions(self, node, content: str, functions=None):
        """Legacy function finder for C/C++ compatibility"""
        if functions is None:
            functions = []

        if node.type == 'function_definition':
            func_text = content[node.start_byte:node.end_byte]
            func_name = self._extract_function_name(node, content)

            functions.append({
                'start': node.start_point,
                'end': node.end_point,
                'text': func_text,
                'name': func_name
            })

        for child in node.children:
            self._find_functions(child, content, functions)

        return functions

    def _extract_function_name(self, func_node, content: str) -> str:
        """Legacy function name extractor for C/C++ compatibility"""
        # Find function declarator to get name
        for child in func_node.children:
            if child.type == 'function_declarator':
                for subchild in child.children:
                    if subchild.type == 'identifier':
                        return content[subchild.start_byte:subchild.end_byte]
        return 'unknown_function'
    def chunk_file_with_semantics(self, file_path: str, content: str, language: str) -> List[Dict[str, Any]]:
        """Enhanced chunking with semantic analysis for C/C++ files"""
        
        # Get basic Tree-sitter chunks
        basic_chunks = self.chunk_file(file_path, content, language)
        
        # Apply semantic enhancement for C/C++ files
        if language in ['c_cpp', 'c', 'cpp'] and len(content) > 1000:  # Only for substantial C/C++ files
            try:
                # Analyze semantics for this file
                semantic_analysis = self.semantic_chunker.analyze_codebase_semantics({file_path: content})
                
                if semantic_analysis['enhanced_chunks']:
                    print(f"🧠 [SEMANTIC] Enhanced {file_path} with {len(semantic_analysis['enhanced_chunks'])} semantic chunks")
                    
                    # Convert semantic chunks to Tree-sitter format
                    enhanced_chunks = []
                    for semantic_chunk in semantic_analysis['enhanced_chunks']:
                        enhanced_chunk = {
                            'content': semantic_chunk['content'],
                            'file_path': file_path,
                            'start_line': 1,  # Semantic chunks may span multiple ranges
                            'end_line': len(semantic_chunk['content'].split('\n')),
                            'chunk_type': semantic_chunk['metadata'].get('chunk_type', 'semantic_enhanced'),
                            'function_name': semantic_chunk['metadata'].get('element_name', 'semantic_cluster'),
                            'semantic_enhanced': True,
                            'context_level': semantic_chunk['metadata'].get('context_level', 'multi_level')
                        }
                        enhanced_chunks.append(enhanced_chunk)
                    
                    return enhanced_chunks
                    
            except Exception as e:
                print(f"⚠️ [SEMANTIC] Enhancement failed for {file_path}: {e}")
                # Fall back to basic chunks
        
        return basic_chunks

