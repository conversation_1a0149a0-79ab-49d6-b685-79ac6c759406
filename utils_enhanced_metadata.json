{"export_date": "2025-07-21T21:21:41.423034", "total_chunks": 302, "statistics": {"languages": {"c_cpp": 299, "csharp": 1, "multi": 2}, "complexities": {"unknown": 302}, "semantic_tags": {"code_implementation": 300, "function": 300, "architectural_pattern": 2, "design": 2, "architecture": 2}}, "chunks": [{"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 44, "end_line": 48, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 50, "end_line": 58, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 60, "end_line": 71, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 106, "end_line": 131, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 295, "end_line": 302, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 1, "end_line": 47, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 46, "end_line": 51, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 54, "end_line": 66, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 69, "end_line": 77, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 118, "end_line": 143, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 146, "end_line": 155, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 158, "end_line": 165, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 168, "end_line": 202, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 205, "end_line": 226, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 1, "end_line": 199, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 35, "end_line": 97, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 100, "end_line": 115, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 118, "end_line": 127, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 130, "end_line": 134, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 137, "end_line": 141, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 144, "end_line": 151, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 154, "end_line": 164, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 167, "end_line": 174, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 177, "end_line": 185, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 188, "end_line": 196, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 1, "end_line": 481, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 1, "end_line": 381, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 94, "end_line": 99, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 186, "end_line": 298, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 301, "end_line": 304, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 307, "end_line": 382, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 385, "end_line": 408, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 412, "end_line": 558, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 466, "end_line": 497, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 499, "end_line": 514, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 515, "end_line": 530, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 561, "end_line": 590, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 598, "end_line": 614, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 618, "end_line": 686, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 690, "end_line": 876, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 879, "end_line": 1069, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 1084, "end_line": 1412, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 1228, "end_line": 1270, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 1272, "end_line": 1395, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 1415, "end_line": 1732, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 1538, "end_line": 1585, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 1589, "end_line": 1716, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 1735, "end_line": 1764, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 1766, "end_line": 1796, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 1799, "end_line": 1920, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 1923, "end_line": 2059, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 2062, "end_line": 2181, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 2184, "end_line": 2341, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 2346, "end_line": 2386, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 2390, "end_line": 2408, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 2411, "end_line": 2486, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 2491, "end_line": 2503, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 2507, "end_line": 2569, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 2572, "end_line": 2655, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 2658, "end_line": 2685, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 2688, "end_line": 2718, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 2723, "end_line": 2994, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 3103, "end_line": 3553, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 1, "end_line": 798, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 50, "end_line": 63, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 66, "end_line": 82, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 85, "end_line": 95, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 98, "end_line": 106, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 109, "end_line": 138, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 141, "end_line": 183, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 186, "end_line": 189, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 192, "end_line": 195, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 198, "end_line": 203, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 206, "end_line": 237, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 1, "end_line": 181, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 1, "end_line": 362, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 80, "end_line": 86, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 89, "end_line": 132, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 135, "end_line": 170, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 173, "end_line": 218, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 221, "end_line": 266, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 269, "end_line": 315, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 318, "end_line": 335, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 338, "end_line": 363, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 366, "end_line": 371, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 374, "end_line": 399, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 402, "end_line": 436, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 441, "end_line": 558, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 1, "end_line": 366, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 36, "end_line": 42, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 45, "end_line": 60, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 63, "end_line": 66, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 69, "end_line": 91, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 94, "end_line": 134, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 137, "end_line": 173, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 176, "end_line": 209, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 212, "end_line": 263, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 353, "end_line": 362, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 1, "end_line": 244, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 40, "end_line": 52, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 54, "end_line": 63, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 84, "end_line": 94, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 112, "end_line": 134, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 152, "end_line": 178, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 186, "end_line": 197, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 204, "end_line": 214, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 222, "end_line": 250, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 270, "end_line": 282, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 302, "end_line": 314, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 333, "end_line": 374, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 394, "end_line": 445, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 466, "end_line": 495, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 505, "end_line": 529, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 540, "end_line": 569, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 587, "end_line": 668, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 671, "end_line": 753, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 756, "end_line": 765, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 1, "end_line": 182, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 48, "end_line": 92, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 95, "end_line": 136, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 139, "end_line": 164, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 167, "end_line": 216, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 219, "end_line": 236, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 239, "end_line": 251, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 254, "end_line": 267, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 270, "end_line": 290, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 293, "end_line": 301, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 304, "end_line": 339, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 349, "end_line": 365, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 1, "end_line": 317, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 125, "end_line": 138, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 142, "end_line": 152, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 155, "end_line": 165, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 168, "end_line": 179, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 182, "end_line": 195, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 200, "end_line": 236, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 239, "end_line": 249, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 252, "end_line": 279, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 281, "end_line": 324, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 326, "end_line": 329, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 331, "end_line": 390, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 1, "end_line": 288, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 91, "end_line": 105, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 108, "end_line": 131, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 134, "end_line": 158, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 187, "end_line": 201, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 231, "end_line": 245, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 248, "end_line": 272, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 276, "end_line": 305, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 308, "end_line": 334, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 364, "end_line": 378, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 1, "end_line": 153, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 94, "end_line": 119, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 128, "end_line": 208, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 217, "end_line": 226, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 235, "end_line": 245, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 255, "end_line": 284, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 296, "end_line": 321, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 333, "end_line": 353, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 363, "end_line": 403, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 415, "end_line": 428, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 437, "end_line": 526, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 536, "end_line": 587, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 597, "end_line": 680, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 685, "end_line": 694, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 697, "end_line": 797, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 800, "end_line": 846, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 849, "end_line": 888, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 891, "end_line": 902, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 905, "end_line": 945, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 949, "end_line": 974, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 1, "end_line": 351, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 40, "end_line": 57, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 59, "end_line": 82, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 85, "end_line": 117, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 120, "end_line": 164, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 167, "end_line": 211, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 214, "end_line": 231, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 234, "end_line": 251, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 1, "end_line": 159, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 51, "end_line": 60, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 63, "end_line": 66, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 69, "end_line": 86, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 91, "end_line": 97, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 1, "end_line": 92, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 36, "end_line": 57, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 1, "end_line": 48, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 34, "end_line": 44, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 47, "end_line": 54, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 58, "end_line": 68, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 72, "end_line": 77, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 81, "end_line": 89, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 92, "end_line": 96, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 100, "end_line": 105, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 108, "end_line": 112, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 1, "end_line": 235, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 37, "end_line": 73, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 76, "end_line": 80, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 83, "end_line": 87, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 90, "end_line": 94, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 97, "end_line": 104, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 107, "end_line": 117, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 121, "end_line": 129, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 132, "end_line": 142, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 146, "end_line": 166, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 169, "end_line": 194, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 1, "end_line": 415, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 237, "end_line": 352, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 1222, "end_line": 1262, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 1264, "end_line": 1293, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 859, "end_line": 893, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 1181, "end_line": 1209, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 1112, "end_line": 1140, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 711, "end_line": 735, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 761, "end_line": 785, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 651, "end_line": 674, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 1308, "end_line": 1327, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 925, "end_line": 944, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 613, "end_line": 632, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 202, "end_line": 219, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 1064, "end_line": 1080, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 828, "end_line": 842, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 107, "end_line": 121, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 1157, "end_line": 1171, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 1096, "end_line": 1110, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 1295, "end_line": 1306, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 1144, "end_line": 1155, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 1083, "end_line": 1094, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 984, "end_line": 994, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 1034, "end_line": 1046, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 1049, "end_line": 1061, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 1019, "end_line": 1031, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 563, "end_line": 575, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 460, "end_line": 470, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 578, "end_line": 587, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 818, "end_line": 825, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 961, "end_line": 969, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 972, "end_line": 980, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 1213, "end_line": 1220, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 597, "end_line": 603, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 695, "end_line": 701, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 677, "end_line": 683, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 686, "end_line": 692, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 911, "end_line": 917, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 738, "end_line": 744, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 635, "end_line": 641, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 952, "end_line": 958, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 795, "end_line": 800, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 903, "end_line": 908, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 810, "end_line": 815, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 540, "end_line": 545, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 525, "end_line": 530, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 997, "end_line": 1002, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 399, "end_line": 404, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 510, "end_line": 515, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 45, "end_line": 50, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 429, "end_line": 434, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 1, "end_line": 790, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 1, "end_line": 1635, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 1, "end_line": 245, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 96, "end_line": 115, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 118, "end_line": 136, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 142, "end_line": 150, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 153, "end_line": 157, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 166, "end_line": 184, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 186, "end_line": 189, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 191, "end_line": 194, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 196, "end_line": 199, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 201, "end_line": 269, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 279, "end_line": 309, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 312, "end_line": 410, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 413, "end_line": 435, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 446, "end_line": 449, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 460, "end_line": 493, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 503, "end_line": 527, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 530, "end_line": 541, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 544, "end_line": 634, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 637, "end_line": 653, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 1, "end_line": 212, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 36, "end_line": 61, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 64, "end_line": 76, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 79, "end_line": 90, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 93, "end_line": 98, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 101, "end_line": 106, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 1, "end_line": 222, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 61, "end_line": 103, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 122, "end_line": 163, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 207, "end_line": 248, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 266, "end_line": 276, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 293, "end_line": 368, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 385, "end_line": 421, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 611, "end_line": 617, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 621, "end_line": 624, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 659, "end_line": 729, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 1, "end_line": 210, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 1, "end_line": 243, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 29, "end_line": 39, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 41, "end_line": 55, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "csharp", "start_line": 6, "end_line": 22, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "c_cpp", "start_line": 1, "end_line": 67, "semantic_tags": ["code_implementation", "function"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "multi", "start_line": 0, "end_line": 0, "semantic_tags": ["architectural_pattern", "design", "architecture"], "complexity_metrics": {}, "quality_indicators": {}}, {"chunk_id": "unknown", "filepath": "unknown", "type": "unknown", "language": "multi", "start_line": 0, "end_line": 0, "semantic_tags": ["architectural_pattern", "design", "architecture"], "complexity_metrics": {}, "quality_indicators": {}}]}