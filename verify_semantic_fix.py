#!/usr/bin/env python3
"""
Final verification script for the semantic analysis fix
Demonstrates that the semantic_analysis variable is now properly used
"""

import sys
import os
import json
from typing import Dict, List, Any

# Add current directory to path for imports
sys.path.append('.')

from semantic_chunking_enhancement import <PERSON>man<PERSON><PERSON><PERSON><PERSON>, integrate_semantic_chunking
from tree_sitter_chunker import TreeSitterChunker

def demonstrate_fix():
    """Demonstrate that the semantic_analysis variable fix is working"""
    print("🔧 SEMANTIC ANALYSIS FIX VERIFICATION")
    print("=" * 50)
    print()
    
    # Sample C code with multiple functions
    sample_code = '''
#include <stdio.h>
#include <stdlib.h>
#include <string.h>

/**
 * Memory allocation wrapper
 */
void* safe_malloc(size_t size) {
    void* ptr = malloc(size);
    if (!ptr) {
        fprintf(stderr, "Memory allocation failed\\n");
        exit(1);
    }
    return ptr;
}

/**
 * String duplication function
 */
char* safe_strdup(const char* str) {
    if (!str) return NULL;
    
    size_t len = strlen(str) + 1;
    char* copy = safe_malloc(len);
    strcpy(copy, str);
    return copy;
}

/**
 * Memory cleanup function
 */
void safe_free(void** ptr) {
    if (ptr && *ptr) {
        free(*ptr);
        *ptr = NULL;
    }
}
'''
    
    print("1. Creating enhanced chunker with semantic analysis...")
    tree_chunker = TreeSitterChunker()
    enhanced_chunker = integrate_semantic_chunking(tree_chunker)
    
    print("2. Processing C code with semantic enhancement...")
    chunks = enhanced_chunker("test_memory.c", sample_code, "c_cpp")
    
    print(f"3. Generated {len(chunks)} chunks")
    print()
    
    # Analyze the chunks to show the fix is working
    semantic_chunks_count = 0
    total_semantic_elements = 0
    total_relevant_elements = 0
    
    for i, chunk in enumerate(chunks, 1):
        if isinstance(chunk, dict) and 'metadata' in chunk:
            metadata = chunk['metadata']
            
            print(f"📦 Chunk {i}:")
            print(f"   Type: {chunk.get('chunk_type', 'unknown')}")
            print(f"   Function: {chunk.get('function_name', 'N/A')}")
            
            if metadata.get('semantic_analysis') is True:
                semantic_chunks_count += 1
                print("   ✅ Has semantic enhancement")
                
                # Show the semantic data that's now being used (the fix)
                if 'semantic_elements' in metadata:
                    elements_count = len(metadata['semantic_elements'])
                    total_semantic_elements += elements_count
                    print(f"   🧠 Semantic elements: {elements_count}")
                
                if 'semantic_relationships' in metadata:
                    relationships_count = len(metadata['semantic_relationships'])
                    print(f"   🔗 Relationships: {relationships_count}")
                
                if 'semantic_clusters' in metadata:
                    clusters_count = len(metadata['semantic_clusters'])
                    print(f"   🎯 Clusters: {clusters_count}")
                
                if 'relevant_semantic_elements' in metadata:
                    relevant_count = len(metadata['relevant_semantic_elements'])
                    total_relevant_elements += relevant_count
                    print(f"   🎯 Relevant elements: {relevant_count}")
                    
                    # Show some relevant elements
                    if relevant_count > 0:
                        relevant_names = list(metadata['relevant_semantic_elements'].keys())[:3]
                        print(f"   📋 Sample elements: {', '.join(relevant_names)}")
            else:
                print("   ⚪ No semantic enhancement")
            
            print()
    
    print("=" * 50)
    print("VERIFICATION SUMMARY")
    print("=" * 50)
    print(f"Total chunks: {len(chunks)}")
    print(f"Semantically enhanced chunks: {semantic_chunks_count}")
    print(f"Total semantic elements found: {total_semantic_elements}")
    print(f"Total relevant elements matched: {total_relevant_elements}")
    print()
    
    # Verify the fix worked
    if semantic_chunks_count > 0 and total_semantic_elements > 0:
        print("✅ SUCCESS: semantic_analysis variable is now properly used!")
        print("   - Semantic analysis data is being extracted")
        print("   - Metadata is being populated with actual analysis results")
        print("   - Relevant elements are being matched to chunk content")
        return True
    else:
        print("❌ FAILURE: semantic_analysis variable is still not being used properly")
        return False

def show_before_after_comparison():
    """Show what the fix changed"""
    print("\n🔄 BEFORE vs AFTER COMPARISON")
    print("=" * 50)
    
    print("BEFORE (the bug):")
    print("   semantic_analysis = semantic_chunker.analyze_codebase_semantics(...)")
    print("   # Variable assigned but never used!")
    print("   enhanced_chunk['metadata']['semantic_analysis'] = True  # Just a boolean flag")
    print()
    
    print("AFTER (the fix):")
    print("   semantic_analysis = semantic_chunker.analyze_codebase_semantics(...)")
    print("   # Now we actually USE the semantic_analysis data:")
    print("   enhanced_chunk['metadata']['semantic_elements'] = semantic_analysis.get('elements', {})")
    print("   enhanced_chunk['metadata']['semantic_relationships'] = semantic_analysis.get('relationships', [])")
    print("   enhanced_chunk['metadata']['semantic_clusters'] = semantic_analysis.get('clusters', [])")
    print("   # Plus relevant element matching logic")
    print()

def test_with_real_file():
    """Test with a real file from the codebase"""
    print("📁 TESTING WITH REAL CODEBASE FILE")
    print("=" * 50)
    
    from pathlib import Path
    utils_path = Path("source_code/utils")
    
    if not utils_path.exists():
        print("❌ Utils codebase not found")
        return False
    
    # Find a C file
    c_files = list(utils_path.glob("*.c"))
    if not c_files:
        c_files = list(utils_path.glob("**/*.c"))
    
    if not c_files:
        print("❌ No C files found")
        return False
    
    test_file = c_files[0]
    print(f"Testing with: {test_file.name}")
    
    try:
        with open(test_file, 'r', encoding='utf-8', errors='ignore') as f:
            content = f.read()
        
        if len(content) < 200:
            print("⚠️ File too small for meaningful test")
            return False
        
        # Process the file
        tree_chunker = TreeSitterChunker()
        enhanced_chunker = integrate_semantic_chunking(tree_chunker)
        chunks = enhanced_chunker(str(test_file), content, "c_cpp")
        
        # Count semantic enhancements
        semantic_count = 0
        element_count = 0
        
        for chunk in chunks:
            if isinstance(chunk, dict) and 'metadata' in chunk:
                metadata = chunk['metadata']
                if metadata.get('semantic_analysis') is True:
                    semantic_count += 1
                    if 'semantic_elements' in metadata:
                        element_count += len(metadata['semantic_elements'])
        
        print(f"✅ Processed {test_file.name}:")
        print(f"   Total chunks: {len(chunks)}")
        print(f"   Semantic chunks: {semantic_count}")
        print(f"   Total elements: {element_count}")
        
        return semantic_count > 0 and element_count > 0
        
    except Exception as e:
        print(f"❌ Error processing file: {e}")
        return False

if __name__ == "__main__":
    print("🧠 SEMANTIC ANALYSIS IMPLEMENTATION VERIFICATION")
    print("=" * 60)
    print()
    
    # Run verification tests
    success1 = demonstrate_fix()
    show_before_after_comparison()
    success2 = test_with_real_file()
    
    print("\n" + "=" * 60)
    print("FINAL VERIFICATION RESULT")
    print("=" * 60)
    
    if success1 and success2:
        print("🎉 ALL TESTS PASSED!")
        print("✅ The semantic_analysis variable fix is working correctly")
        print("✅ Semantic metadata is being properly populated")
        print("✅ Integration with existing chunking system is successful")
        print("\nThe IDE issue has been resolved! 🎯")
    else:
        print("❌ Some tests failed")
        print("The fix may need additional work")
