#!/usr/bin/env python3
"""
Comprehensive test suite for semantic analysis implementation
Tests the SemanticChunker and enhanced chunking functionality
"""

import sys
import os
import json
import time
from typing import Dict, List, Any
from pathlib import Path

# Add current directory to path for imports
sys.path.append('.')

from semantic_chunking_enhancement import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, EnhancedQueryProcessor, integrate_semantic_chunking
from tree_sitter_chunker import TreeSitterChunker

class SemanticAnalysisTestSuite:
    """Comprehensive test suite for semantic analysis"""
    
    def __init__(self):
        self.test_results = []
        self.semantic_chunker = SemanticChunker()
        self.tree_sitter_chunker = TreeSitterChunker()
        
    def log_test_result(self, test_name: str, success: bool, details: str = "", execution_time: float = 0):
        """Log test result"""
        result = {
            "test_name": test_name,
            "success": success,
            "details": details,
            "execution_time": execution_time,
            "timestamp": time.time()
        }
        self.test_results.append(result)
        
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status} {test_name}")
        if details:
            print(f"   Details: {details}")
        if execution_time > 0:
            print(f"   Time: {execution_time:.3f}s")
        print()

    def get_sample_c_code(self) -> str:
        """Get sample C code for testing"""
        return '''
/**
 * Memory management utilities
 * Provides low-level memory allocation and deallocation
 */
#include <stdlib.h>
#include <string.h>

#define MAX_MEMORY_BLOCKS 1024
#define MEMORY_ALIGNMENT 8

typedef struct memory_block {
    void* ptr;
    size_t size;
    int is_free;
    struct memory_block* next;
} memory_block_t;

static memory_block_t* memory_pool = NULL;
static int total_blocks = 0;

/**
 * Initialize the memory management system
 * @return 0 on success, -1 on failure
 */
int tmwmem_init(void) {
    memory_pool = malloc(sizeof(memory_block_t) * MAX_MEMORY_BLOCKS);
    if (!memory_pool) {
        return -1;
    }
    memset(memory_pool, 0, sizeof(memory_block_t) * MAX_MEMORY_BLOCKS);
    total_blocks = 0;
    return 0;
}

/**
 * Allocate memory block
 * @param size Size in bytes to allocate
 * @return Pointer to allocated memory or NULL on failure
 */
void* tmwmem_alloc(size_t size) {
    if (size == 0 || total_blocks >= MAX_MEMORY_BLOCKS) {
        return NULL;
    }
    
    // Align size to memory boundary
    size = (size + MEMORY_ALIGNMENT - 1) & ~(MEMORY_ALIGNMENT - 1);
    
    void* ptr = malloc(size);
    if (!ptr) {
        return NULL;
    }
    
    // Find free slot in memory pool
    for (int i = 0; i < MAX_MEMORY_BLOCKS; i++) {
        if (memory_pool[i].ptr == NULL) {
            memory_pool[i].ptr = ptr;
            memory_pool[i].size = size;
            memory_pool[i].is_free = 0;
            total_blocks++;
            break;
        }
    }
    
    return ptr;
}

/**
 * Free allocated memory block
 * @param ptr Pointer to memory to free
 */
void tmwmem_free(void* ptr) {
    if (!ptr) return;
    
    for (int i = 0; i < MAX_MEMORY_BLOCKS; i++) {
        if (memory_pool[i].ptr == ptr) {
            free(ptr);
            memory_pool[i].ptr = NULL;
            memory_pool[i].size = 0;
            memory_pool[i].is_free = 1;
            total_blocks--;
            break;
        }
    }
}

/**
 * Get available memory statistics
 * @return Number of free blocks
 */
int tmwmem_get_free_blocks(void) {
    return MAX_MEMORY_BLOCKS - total_blocks;
}
'''

    def test_semantic_chunker_initialization(self):
        """Test SemanticChunker initialization"""
        start_time = time.time()
        
        try:
            chunker = SemanticChunker()
            
            # Check if all required attributes are initialized
            assert hasattr(chunker, 'relationships'), "Missing relationships attribute"
            assert hasattr(chunker, 'code_elements'), "Missing code_elements attribute"
            assert hasattr(chunker, 'dependency_graph'), "Missing dependency_graph attribute"
            
            assert isinstance(chunker.relationships, list), "relationships should be a list"
            assert isinstance(chunker.code_elements, dict), "code_elements should be a dict"
            assert isinstance(chunker.dependency_graph, dict), "dependency_graph should be a dict"
            
            execution_time = time.time() - start_time
            self.log_test_result("SemanticChunker Initialization", True, 
                               "All attributes properly initialized", execution_time)
            
        except Exception as e:
            execution_time = time.time() - start_time
            self.log_test_result("SemanticChunker Initialization", False, 
                               f"Error: {str(e)}", execution_time)

    def test_analyze_codebase_semantics(self):
        """Test the analyze_codebase_semantics method"""
        start_time = time.time()
        
        try:
            sample_code = self.get_sample_c_code()
            file_contents = {"test_memory.c": sample_code}
            
            analysis = self.semantic_chunker.analyze_codebase_semantics(file_contents)
            
            # Verify return structure
            assert isinstance(analysis, dict), "Analysis should return a dictionary"
            
            required_keys = ['elements', 'relationships', 'clusters', 'enhanced_chunks']
            for key in required_keys:
                assert key in analysis, f"Missing key: {key}"
            
            # Check elements
            elements = analysis['elements']
            assert isinstance(elements, dict), "Elements should be a dictionary"
            
            # Check relationships
            relationships = analysis['relationships']
            assert isinstance(relationships, list), "Relationships should be a list"
            
            # Check clusters
            clusters = analysis['clusters']
            assert isinstance(clusters, list), "Clusters should be a list"
            
            # Check enhanced chunks
            enhanced_chunks = analysis['enhanced_chunks']
            assert isinstance(enhanced_chunks, list), "Enhanced chunks should be a list"
            
            execution_time = time.time() - start_time
            details = f"Found {len(elements)} elements, {len(relationships)} relationships, {len(clusters)} clusters, {len(enhanced_chunks)} enhanced chunks"
            self.log_test_result("Analyze Codebase Semantics", True, details, execution_time)
            
            return analysis
            
        except Exception as e:
            execution_time = time.time() - start_time
            self.log_test_result("Analyze Codebase Semantics", False, 
                               f"Error: {str(e)}", execution_time)
            return None

    def test_semantic_integration(self):
        """Test the integration with existing chunker"""
        start_time = time.time()
        
        try:
            # Create enhanced chunker
            enhanced_chunker = integrate_semantic_chunking(self.tree_sitter_chunker)
            
            sample_code = self.get_sample_c_code()
            
            # Test with C++ language (should trigger semantic enhancement)
            enhanced_chunks = enhanced_chunker("test_memory.c", sample_code, "c_cpp")
            
            assert isinstance(enhanced_chunks, list), "Should return a list of chunks"
            assert len(enhanced_chunks) > 0, "Should return at least one chunk"
            
            # Check if chunks have semantic metadata
            semantic_enhanced = False
            for chunk in enhanced_chunks:
                if isinstance(chunk, dict) and 'metadata' in chunk:
                    metadata = chunk['metadata']
                    if 'semantic_analysis' in metadata and metadata['semantic_analysis']:
                        semantic_enhanced = True
                        
                        # Check for semantic metadata fields
                        expected_fields = ['semantic_elements', 'semantic_relationships', 'semantic_clusters']
                        for field in expected_fields:
                            assert field in metadata, f"Missing semantic field: {field}"
                        break
            
            assert semantic_enhanced, "At least one chunk should have semantic enhancement"
            
            execution_time = time.time() - start_time
            details = f"Generated {len(enhanced_chunks)} enhanced chunks with semantic metadata"
            self.log_test_result("Semantic Integration", True, details, execution_time)
            
        except Exception as e:
            execution_time = time.time() - start_time
            self.log_test_result("Semantic Integration", False, 
                               f"Error: {str(e)}", execution_time)

    def test_query_processor(self):
        """Test the EnhancedQueryProcessor"""
        start_time = time.time()
        
        try:
            # First get semantic analysis
            sample_code = self.get_sample_c_code()
            analysis = self.semantic_chunker.analyze_codebase_semantics({"test_memory.c": sample_code})
            
            if not analysis:
                raise Exception("Failed to get semantic analysis")
            
            # Test query processor
            query_processor = EnhancedQueryProcessor(self.semantic_chunker)
            
            test_query = "explain tmwmem_alloc function"
            enhanced = query_processor.enhance_query(test_query, analysis['elements'])
            
            assert isinstance(enhanced, dict), "Enhanced query should return a dictionary"
            assert 'enhanced_query' in enhanced, "Missing enhanced_query field"
            assert 'context_expansion' in enhanced, "Missing context_expansion field"
            
            execution_time = time.time() - start_time
            details = f"Enhanced query: '{enhanced['enhanced_query'][:50]}...'"
            self.log_test_result("Query Processor", True, details, execution_time)
            
        except Exception as e:
            execution_time = time.time() - start_time
            self.log_test_result("Query Processor", False, 
                               f"Error: {str(e)}", execution_time)

    def test_real_codebase_analysis(self):
        """Test with real codebase files"""
        start_time = time.time()
        
        try:
            # Look for utils codebase
            utils_path = Path("source_code/utils")
            if not utils_path.exists():
                self.log_test_result("Real Codebase Analysis", False, 
                                   "Utils codebase not found", time.time() - start_time)
                return
            
            # Find a C file to analyze
            c_files = list(utils_path.glob("*.c"))
            if not c_files:
                c_files = list(utils_path.glob("**/*.c"))
            
            if not c_files:
                self.log_test_result("Real Codebase Analysis", False, 
                                   "No C files found in utils codebase", time.time() - start_time)
                return
            
            # Analyze first C file
            test_file = c_files[0]
            with open(test_file, 'r', encoding='utf-8', errors='ignore') as f:
                content = f.read()
            
            if len(content) < 100:  # Skip very small files
                self.log_test_result("Real Codebase Analysis", False, 
                                   f"File too small: {test_file}", time.time() - start_time)
                return
            
            analysis = self.semantic_chunker.analyze_codebase_semantics({str(test_file): content})
            
            assert analysis is not None, "Analysis should not be None"
            assert len(analysis['elements']) > 0, "Should find some code elements"
            
            execution_time = time.time() - start_time
            details = f"Analyzed {test_file.name}: {len(analysis['elements'])} elements, {len(analysis['enhanced_chunks'])} chunks"
            self.log_test_result("Real Codebase Analysis", True, details, execution_time)
            
        except Exception as e:
            execution_time = time.time() - start_time
            self.log_test_result("Real Codebase Analysis", False, 
                               f"Error: {str(e)}", execution_time)

    def run_all_tests(self):
        """Run all semantic analysis tests"""
        print("🧠 SEMANTIC ANALYSIS TEST SUITE")
        print("=" * 60)
        print()
        
        # Run individual tests
        self.test_semantic_chunker_initialization()
        self.test_analyze_codebase_semantics()
        self.test_semantic_integration()
        self.test_query_processor()
        self.test_real_codebase_analysis()
        
        # Print summary
        print("=" * 60)
        print("TEST SUMMARY")
        print("=" * 60)
        
        total_tests = len(self.test_results)
        passed_tests = sum(1 for result in self.test_results if result['success'])
        failed_tests = total_tests - passed_tests
        
        print(f"Total Tests: {total_tests}")
        print(f"Passed: {passed_tests}")
        print(f"Failed: {failed_tests}")
        print(f"Success Rate: {(passed_tests/total_tests)*100:.1f}%")
        
        if failed_tests > 0:
            print("\nFAILED TESTS:")
            for result in self.test_results:
                if not result['success']:
                    print(f"  ❌ {result['test_name']}: {result['details']}")
        
        return self.test_results

if __name__ == "__main__":
    test_suite = SemanticAnalysisTestSuite()
    results = test_suite.run_all_tests()
    
    # Save results to file
    with open("semantic_analysis_test_results.json", "w") as f:
        json.dump(results, f, indent=2)
    
    print(f"\nTest results saved to semantic_analysis_test_results.json")
