# OpenWebUI RAG Code Server

A comprehensive, multi-language code analysis server with RAG (Retrieval-Augmented Generation) capabilities, designed for intelligent code search, analysis, and AI-powered insights across 27+ programming languages.

## 🚀 Overview

The OpenWebUI RAG Code Server is an advanced code analysis platform that combines:

- **Multi-Language Support**: 45+ programming languages with specialized processors
- **RAG Technology**: Vector-based semantic code search using ChromaDB
- **AI Integration**: Ollama-powered code explanations and insights
- **OpenWebUI Integration**: Seamless tool integration for natural language queries
- **Web Management**: Comprehensive dashboard for monitoring and control
- **Docker Deployment**: Production-ready containerized deployment

## ✨ Key Features

### 🌍 **Comprehensive Language Support**
- **Specialized Processors**: C/C++, Python, C#, JavaScript, TypeScript, Metta
- **Generic Support**: Java, Go, Rust, Swift, Kotlin, Scala, Ruby, PHP, and more
- **Language-Aware Analysis**: Context-sensitive parsing and semantic understanding
- **Cross-Language Queries**: Find patterns and relationships across different languages

### 🔍 **Intelligent Code Analysis**
- **Semantic Search**: Vector-based code search using embeddings
- **Function Extraction**: Automatic extraction of functions, classes, methods
- **Pattern Recognition**: Domain-specific semantic patterns (API, database, security, etc.)
- **Relationship Mapping**: Header-implementation pairs, imports, dependencies

### 🤖 **AI-Powered Insights**
- **Code Explanations**: Natural language explanations of code functionality
- **Architecture Analysis**: System-level design pattern recognition
- **Code Quality Assessment**: Best practices and improvement suggestions
- **Migration Assistance**: Cross-language porting recommendations

### 🛠️ **Developer Tools**
- **OpenWebUI Tool**: Natural language interface for code queries
- **REST API**: Comprehensive API for programmatic access
- **Web Dashboard**: Real-time monitoring and management interface
- **Unit Testing**: Comprehensive test suite with 95%+ coverage

## 📋 Quick Start

### Prerequisites
- Docker and Docker Compose
- Python 3.8+ (for local development)
- Access to Ollama service (for AI features)

### 1. Clone and Setup
```bash
git clone <repository-url>
cd openwebui_rag_code_server

# Place your source code in the source_code directory
mkdir -p source_code
# Copy your codebases to source_code/your_project_name/
```

### 2. Deploy with Docker
```bash
# Build and start all services
docker-compose up --build -d

# Verify deployment
curl http://localhost:5002/health
```

### 3. Access Interfaces
- **Code Analysis API**: http://localhost:5002
- **Web Management Dashboard**: http://localhost:5003
- **OpenWebUI Integration**: Install tool from `open_webui_code_analyzer_tool.py`

## 📚 Documentation

| Document | Description |
|----------|-------------|
| [INSTALLATION.md](docs/INSTALLATION.md) | Complete installation and setup guide |
| [USER_GUIDE.md](docs/USER_GUIDE.md) | Comprehensive user guide and API reference |
| [ARCHITECTURE.md](docs/ARCHITECTURE.md) | Technical architecture and design documentation |
| [DEPLOYMENT.md](docs/DEPLOYMENT.md) | Production deployment and configuration |
| [OPENWEBUI_INTEGRATION.md](docs/OPENWEBUI_INTEGRATION.md) | OpenWebUI tool installation and usage |
| [TROUBLESHOOTING.md](docs/TROUBLESHOOTING.md) | Common issues and solutions |
| [CONTRIBUTING.md](docs/CONTRIBUTING.md) | Developer contribution guide |

## 🎯 Use Cases

### **Code Discovery**
```bash
# Find memory management functions across C/C++ codebases
search_code("memory allocation", filter_language="cpp")

# Discover API endpoints in web services
search_code("REST endpoint", filter_language="python")
```

### **Architecture Analysis**
```bash
# Understand system design patterns
ask_about_code("What design patterns are used in this codebase?")

# Analyze cross-language communication
ask_about_code("How do the Python and C# services communicate?")
```

### **Code Migration**
```bash
# Find porting opportunities
ask_about_code("What C++ components could be ported to Rust?")

# Compare implementations
search_code("database connection", n_results=10)  # Shows all languages
```

## 🏗️ Architecture

### Core Components
- **Language Framework**: Modular, extensible language processing system
- **Processing Pipeline**: Configurable stages for code analysis and chunking
- **Vector Database**: ChromaDB for semantic search and retrieval
- **AI Integration**: Ollama for natural language processing and explanations
- **Web Services**: FastAPI-based REST API and management interface

### Language Processors
- **Specialized**: Custom processors for major languages (C/C++, Python, C#, JS)
- **Generic**: Tree-sitter based processing for all other supported languages
- **Extensible**: Plugin architecture for adding new language support

## 📊 Supported Languages

### Programming Languages (25+)
Python, C, C++, C#, JavaScript, TypeScript, Java, Go, Rust, Swift, Kotlin, Scala, Ruby, PHP, Perl, Lua, Bash, PowerShell, Haskell, Erlang, Elixir, Clojure, Lisp, Scheme, TCL

### Specialized Languages (10+)
SQL, HTML, CSS, SCSS, JSON, YAML, XML, TOML, Markdown, TeX

### Hardware & Scientific (8+)
Verilog, VHDL, Assembly, MATLAB, R, Fortran, Pascal, Makefile, CMake

### AI/Logic Programming (2+)
Metta, Prolog

## 🔧 Configuration

### Environment Variables
```bash
OLLAMA_HOST=http://ollama:11434              # AI service endpoint
CHROMA_DB_BASE_PATH=./chroma_db              # Vector database storage
SOURCE_CODE_BASE_PATH=./source_code          # Source code directory
WEB_MANAGEMENT_PORT=5003                     # Web dashboard port
```

### Docker Compose
The project includes a complete Docker Compose configuration with:
- Code Analysis Server (port 5002)
- Web Management Interface (port 5003)
- ChromaDB vector storage
- Ollama AI service integration

## 🧪 Testing

### Run All Tests
```bash
# Unit tests
./run_unit_tests.sh

# Integration tests
python run_all_tests.py

# Performance tests
python performance_test_suite.py
```

### Test Coverage
- **Unit Tests**: 95%+ coverage across all core components
- **Integration Tests**: End-to-end API and tool functionality
- **Performance Tests**: Load testing and benchmarking

## 🚀 Production Deployment

### Docker Deployment
```bash
# Production deployment
docker-compose -f docker-compose.yml up -d

# Health check
curl http://your-server:5002/health
```

### Monitoring
- **Health Endpoints**: Real-time system status
- **Performance Metrics**: Response times and success rates
- **Resource Monitoring**: GPU utilization and memory usage
- **Log Aggregation**: Structured logging with rotation

## 🤝 Contributing

We welcome contributions! Please see [CONTRIBUTING.md](CONTRIBUTING.md) for:
- Development setup
- Code style guidelines
- Testing requirements
- Pull request process

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

For issues, questions, or feature requests:
1. Check [TROUBLESHOOTING.md](TROUBLESHOOTING.md)
2. Review existing GitHub issues
3. Create a new issue with detailed information
4. Join our community discussions

## 🎉 Acknowledgments

- **Tree-sitter**: For robust language parsing
- **ChromaDB**: For vector database capabilities
- **Ollama**: For local AI model integration
- **OpenWebUI**: For the extensible chat interface
- **FastAPI**: For the high-performance web framework
