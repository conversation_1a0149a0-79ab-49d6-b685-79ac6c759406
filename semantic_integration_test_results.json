[{"test_name": "Semantic Analysis Variable Usage Fix", "success": true, "details": "Found 3 chunks with semantic enhancement and relevant element matching", "timestamp": 1753187856.1965983}, {"test_name": "Semantic Metadata Structure", "success": true, "details": "Metadata structure validated with 7 elements", "timestamp": 1753187856.1999698, "data": {"elements_count": 7, "relationships_count": 1, "clusters_count": 1}}, {"test_name": "Non-C/C++ File Handling", "success": true, "details": "Python file correctly bypassed semantic enhancement (2 chunks)", "timestamp": 1753187856.232766}, {"test_name": "Real Utils File Test", "success": true, "details": "Processed tmwappl.c: 8 chunks, 8 semantic, 80 elements", "timestamp": 1753187856.2425532, "data": {"file": "source_code\\utils\\tmwappl.c", "total_chunks": 8, "semantic_chunks": 8, "total_elements": 80}}]