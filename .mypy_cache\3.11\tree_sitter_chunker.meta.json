{"data_mtime": 1753185902, "dep_lines": [1, 2, 3, 4, 5, 6, 1, 1, 1, 1, 1, 1], "dep_prios": [5, 5, 5, 10, 5, 5, 5, 30, 30, 30, 30, 30], "dependencies": ["tree_sitter_language_pack", "tree_sitter", "typing", "os", "embedding_config", "semantic_chunking_enhancement", "builtins", "_frozen_importlib", "_typeshed", "abc", "enum", "typing_extensions"], "hash": "931fd3ec6603504ba71de575fc34d8b6a05cb9e9", "id": "tree_sitter_chunker", "ignore_all": false, "interface_hash": "572d6f6eca99e65a4742300c0a91e46ce26352ab", "mtime": 1753185901, "options": {"allow_redefinition": false, "allow_untyped_globals": false, "always_false": [], "always_true": [], "bazel": false, "check_untyped_defs": false, "disable_bytearray_promotion": false, "disable_error_code": [], "disable_memoryview_promotion": false, "disabled_error_codes": [], "disallow_any_decorated": false, "disallow_any_explicit": false, "disallow_any_expr": false, "disallow_any_generics": false, "disallow_any_unimported": false, "disallow_incomplete_defs": false, "disallow_subclassing_any": false, "disallow_untyped_calls": false, "disallow_untyped_decorators": false, "disallow_untyped_defs": false, "enable_error_code": [], "enabled_error_codes": [], "extra_checks": false, "follow_imports": "normal", "follow_imports_for_stubs": false, "follow_untyped_imports": false, "ignore_errors": false, "ignore_missing_imports": false, "implicit_optional": false, "implicit_reexport": true, "local_partial_types": false, "mypyc": false, "old_type_inference": false, "platform": "win32", "plugins": [], "strict_bytes": false, "strict_concatenate": false, "strict_equality": false, "strict_optional": true, "warn_no_return": true, "warn_return_any": false, "warn_unreachable": false, "warn_unused_ignores": false}, "path": "C:\\home-repos\\openwebui_rag_code_server\\tree_sitter_chunker.py", "plugin_data": null, "size": 18528, "suppressed": [], "version_id": "1.15.0"}