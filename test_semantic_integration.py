#!/usr/bin/env python3
"""
Integration test for semantic analysis with the existing chunking system
Tests the fixed semantic_analysis variable usage and metadata enhancement
"""

import sys
import os
import json
import time
from typing import Dict, List, Any
from pathlib import Path

# Add current directory to path for imports
sys.path.append('.')

from semantic_chunking_enhancement import SemanticChun<PERSON>, integrate_semantic_chunking
from tree_sitter_chunker import TreeSitterChunker

class SemanticIntegrationTester:
    """Test semantic analysis integration with existing systems"""
    
    def __init__(self):
        self.results = []
        
    def log_result(self, test_name: str, success: bool, details: str = "", data: Any = None):
        """Log test result"""
        result = {
            "test_name": test_name,
            "success": success,
            "details": details,
            "timestamp": time.time()
        }
        if data:
            result["data"] = data
        self.results.append(result)
        
        status = "✅ PASS" if success else "❌ FAIL"
        print(f"{status} {test_name}")
        if details:
            print(f"   {details}")
        print()

    def test_semantic_analysis_variable_usage(self):
        """Test that semantic_analysis variable is properly used (the fix we made)"""
        print("🔧 Testing semantic_analysis variable usage fix...")
        
        try:
            # Create enhanced chunker
            tree_chunker = TreeSitterChunker()
            enhanced_chunker = integrate_semantic_chunking(tree_chunker)
            
            # Sample C code
            sample_code = '''
#include <stdio.h>
#include <stdlib.h>

/**
 * Test memory allocation function
 */
void* test_alloc(size_t size) {
    return malloc(size);
}

/**
 * Test memory deallocation function
 */
void test_free(void* ptr) {
    free(ptr);
}

int main() {
    void* ptr = test_alloc(100);
    test_free(ptr);
    return 0;
}
'''
            
            # Process with C++ language to trigger semantic enhancement
            chunks = enhanced_chunker("test.c", sample_code, "c_cpp")
            
            # Verify chunks were created
            assert len(chunks) > 0, "Should create at least one chunk"
            
            # Check for semantic metadata in chunks
            semantic_metadata_found = False
            semantic_elements_found = False
            relevant_elements_found = False
            
            for chunk in chunks:
                if isinstance(chunk, dict) and 'metadata' in chunk:
                    metadata = chunk['metadata']
                    
                    # Check if semantic analysis was applied
                    if metadata.get('semantic_analysis') is True:
                        semantic_metadata_found = True
                        
                        # Check for the new semantic metadata fields we added
                        if 'semantic_elements' in metadata:
                            semantic_elements_found = True
                            
                        if 'relevant_semantic_elements' in metadata:
                            relevant_elements_found = True
                            
                        # Print metadata for inspection
                        print(f"   📊 Chunk metadata keys: {list(metadata.keys())}")
                        if 'semantic_elements' in metadata:
                            print(f"   🧠 Semantic elements count: {len(metadata['semantic_elements'])}")
                        if 'relevant_semantic_elements' in metadata:
                            print(f"   🎯 Relevant elements count: {len(metadata['relevant_semantic_elements'])}")
            
            # Verify the fix worked
            assert semantic_metadata_found, "Should have semantic metadata"
            assert semantic_elements_found, "Should have semantic_elements field (from our fix)"
            
            details = f"Found {len(chunks)} chunks with semantic enhancement"
            if relevant_elements_found:
                details += " and relevant element matching"
            
            self.log_result("Semantic Analysis Variable Usage Fix", True, details)
            
        except Exception as e:
            self.log_result("Semantic Analysis Variable Usage Fix", False, f"Error: {str(e)}")

    def test_metadata_structure(self):
        """Test the structure of semantic metadata"""
        print("🏗️ Testing semantic metadata structure...")
        
        try:
            tree_chunker = TreeSitterChunker()
            enhanced_chunker = integrate_semantic_chunking(tree_chunker)
            
            # More complex C code to generate richer semantic data
            complex_code = '''
#include <stdio.h>
#include <stdlib.h>
#include <string.h>

#define BUFFER_SIZE 1024
#define MAX_ITEMS 100

typedef struct item {
    int id;
    char name[64];
    double value;
} item_t;

static item_t* items[MAX_ITEMS];
static int item_count = 0;

/**
 * Initialize the item system
 */
int init_items(void) {
    memset(items, 0, sizeof(items));
    item_count = 0;
    return 0;
}

/**
 * Create a new item
 */
item_t* create_item(int id, const char* name, double value) {
    if (item_count >= MAX_ITEMS) {
        return NULL;
    }
    
    item_t* item = malloc(sizeof(item_t));
    if (!item) {
        return NULL;
    }
    
    item->id = id;
    strncpy(item->name, name, sizeof(item->name) - 1);
    item->name[sizeof(item->name) - 1] = '\\0';
    item->value = value;
    
    items[item_count++] = item;
    return item;
}

/**
 * Find item by ID
 */
item_t* find_item(int id) {
    for (int i = 0; i < item_count; i++) {
        if (items[i] && items[i]->id == id) {
            return items[i];
        }
    }
    return NULL;
}

/**
 * Cleanup all items
 */
void cleanup_items(void) {
    for (int i = 0; i < item_count; i++) {
        if (items[i]) {
            free(items[i]);
            items[i] = NULL;
        }
    }
    item_count = 0;
}
'''
            
            chunks = enhanced_chunker("complex_test.c", complex_code, "c_cpp")
            
            # Find a chunk with semantic metadata
            semantic_chunk = None
            for chunk in chunks:
                if isinstance(chunk, dict) and 'metadata' in chunk:
                    metadata = chunk['metadata']
                    if metadata.get('semantic_analysis') is True:
                        semantic_chunk = chunk
                        break
            
            assert semantic_chunk is not None, "Should find at least one semantically enhanced chunk"
            
            metadata = semantic_chunk['metadata']
            
            # Check required fields from our fix
            required_fields = [
                'semantic_analysis',
                'context_level', 
                'semantic_elements',
                'semantic_relationships',
                'semantic_clusters'
            ]
            
            for field in required_fields:
                assert field in metadata, f"Missing required field: {field}"
            
            # Check data types
            assert isinstance(metadata['semantic_elements'], dict), "semantic_elements should be dict"
            assert isinstance(metadata['semantic_relationships'], list), "semantic_relationships should be list"
            assert isinstance(metadata['semantic_clusters'], list), "semantic_clusters should be list"
            
            # Check if relevant elements were found
            if 'relevant_semantic_elements' in metadata:
                assert isinstance(metadata['relevant_semantic_elements'], dict), "relevant_semantic_elements should be dict"
                print(f"   🎯 Found {len(metadata['relevant_semantic_elements'])} relevant elements")
            
            details = f"Metadata structure validated with {len(metadata['semantic_elements'])} elements"
            self.log_result("Semantic Metadata Structure", True, details, {
                "elements_count": len(metadata['semantic_elements']),
                "relationships_count": len(metadata['semantic_relationships']),
                "clusters_count": len(metadata['semantic_clusters'])
            })
            
        except Exception as e:
            self.log_result("Semantic Metadata Structure", False, f"Error: {str(e)}")

    def test_non_cpp_files(self):
        """Test that non-C/C++ files don't get semantic enhancement"""
        print("🚫 Testing non-C/C++ file handling...")
        
        try:
            tree_chunker = TreeSitterChunker()
            enhanced_chunker = integrate_semantic_chunking(tree_chunker)
            
            # Python code (should not get semantic enhancement)
            python_code = '''
def hello_world():
    print("Hello, World!")

def add_numbers(a, b):
    return a + b

if __name__ == "__main__":
    hello_world()
    result = add_numbers(5, 3)
    print(f"Result: {result}")
'''
            
            chunks = enhanced_chunker("test.py", python_code, "python")
            
            # Check that no chunks have semantic enhancement
            semantic_enhanced = False
            for chunk in chunks:
                if isinstance(chunk, dict) and 'metadata' in chunk:
                    metadata = chunk['metadata']
                    if metadata.get('semantic_analysis') is True:
                        semantic_enhanced = True
                        break
            
            assert not semantic_enhanced, "Python files should not get semantic enhancement"
            
            self.log_result("Non-C/C++ File Handling", True, 
                          f"Python file correctly bypassed semantic enhancement ({len(chunks)} chunks)")
            
        except Exception as e:
            self.log_result("Non-C/C++ File Handling", False, f"Error: {str(e)}")

    def test_real_utils_file(self):
        """Test with a real file from the utils codebase"""
        print("📁 Testing with real utils codebase file...")
        
        try:
            utils_path = Path("source_code/utils")
            if not utils_path.exists():
                self.log_result("Real Utils File Test", False, "Utils codebase not found")
                return
            
            # Find C files
            c_files = list(utils_path.glob("*.c"))
            if not c_files:
                c_files = list(utils_path.glob("**/*.c"))
            
            if not c_files:
                self.log_result("Real Utils File Test", False, "No C files found in utils")
                return
            
            # Test with first substantial C file
            test_file = None
            for c_file in c_files:
                try:
                    with open(c_file, 'r', encoding='utf-8', errors='ignore') as f:
                        content = f.read()
                    if len(content) > 500:  # Find a substantial file
                        test_file = c_file
                        break
                except:
                    continue
            
            if not test_file:
                self.log_result("Real Utils File Test", False, "No substantial C files found")
                return
            
            # Process the file
            tree_chunker = TreeSitterChunker()
            enhanced_chunker = integrate_semantic_chunking(tree_chunker)
            
            chunks = enhanced_chunker(str(test_file), content, "c_cpp")
            
            # Analyze results
            semantic_chunks = 0
            total_elements = 0
            
            for chunk in chunks:
                if isinstance(chunk, dict) and 'metadata' in chunk:
                    metadata = chunk['metadata']
                    if metadata.get('semantic_analysis') is True:
                        semantic_chunks += 1
                        if 'semantic_elements' in metadata:
                            total_elements += len(metadata['semantic_elements'])
            
            details = f"Processed {test_file.name}: {len(chunks)} chunks, {semantic_chunks} semantic, {total_elements} elements"
            self.log_result("Real Utils File Test", True, details, {
                "file": str(test_file),
                "total_chunks": len(chunks),
                "semantic_chunks": semantic_chunks,
                "total_elements": total_elements
            })
            
        except Exception as e:
            self.log_result("Real Utils File Test", False, f"Error: {str(e)}")

    def run_all_tests(self):
        """Run all integration tests"""
        print("🔗 SEMANTIC ANALYSIS INTEGRATION TESTS")
        print("=" * 60)
        print()
        
        self.test_semantic_analysis_variable_usage()
        self.test_metadata_structure()
        self.test_non_cpp_files()
        self.test_real_utils_file()
        
        # Summary
        print("=" * 60)
        print("INTEGRATION TEST SUMMARY")
        print("=" * 60)
        
        total = len(self.results)
        passed = sum(1 for r in self.results if r['success'])
        
        print(f"Total Tests: {total}")
        print(f"Passed: {passed}")
        print(f"Failed: {total - passed}")
        print(f"Success Rate: {(passed/total)*100:.1f}%")
        
        if passed < total:
            print("\nFAILED TESTS:")
            for result in self.results:
                if not result['success']:
                    print(f"  ❌ {result['test_name']}: {result['details']}")
        
        return self.results

if __name__ == "__main__":
    tester = SemanticIntegrationTester()
    results = tester.run_all_tests()
    
    # Save results
    with open("semantic_integration_test_results.json", "w") as f:
        json.dump(results, f, indent=2)
    
    print(f"\nIntegration test results saved to semantic_integration_test_results.json")
