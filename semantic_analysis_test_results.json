[{"test_name": "SemanticChunker Initialization", "success": true, "details": "All attributes properly initialized", "execution_time": 0.0, "timestamp": 1753187361.2504187}, {"test_name": "Analyze Codebase Semantics", "success": true, "details": "Found 8 elements, 5 relationships, 1 clusters, 3 enhanced chunks", "execution_time": 0.002438783645629883, "timestamp": 1753187361.2528574}, {"test_name": "Semantic Integration", "success": true, "details": "Generated 4 enhanced chunks with semantic metadata", "execution_time": 0.012682676315307617, "timestamp": 1753187361.2655401}, {"test_name": "Query Processor", "success": true, "details": "Enhanced query: 'explain tmwmem_alloc function tmwmem_alloc tmwmem_...'", "execution_time": 0.001016855239868164, "timestamp": 1753187361.266557}, {"test_name": "Real Codebase Analysis", "success": true, "details": "Analyzed tmwappl.c: 18 elements, 9 chunks", "execution_time": 0.00480198860168457, "timestamp": 1753187361.271359}]