#!/usr/bin/env python3
"""
Semantic Chunking Enhancement System
Implements advanced chunking strategies for better code analysis results

Key Improvements:
1. Semantic Chunking Strategy - Group related functions, include data structures
2. Enhanced Query Processing - Dependency analysis and usage examples  
3. Context-Aware Ranking - Prioritize documentation and configuration
4. Multi-Level Context - Hierarchical context building
"""

import re
from typing import Dict, List, Any, Optional, Set, Tuple, cast
from dataclasses import dataclass
from enum import Enum
import logging

logger = logging.getLogger(__name__)

class ContextLevel(Enum):
    """Multi-level context hierarchy"""
    SIGNATURE = 1      # Function signature + documentation
    STRUCTURE = 2      # Data structures and macros used
    USAGE = 3         # Usage patterns and examples
    CONFIGURATION = 4  # Configuration options that affect behavior

@dataclass
class SemanticRelationship:
    """Represents a relationship between code elements"""
    source_element: str
    target_element: str
    relationship_type: str  # 'calls', 'uses', 'defines', 'configures'
    strength: float        # 0.0 to 1.0
    context: str          # Additional context about the relationship

@dataclass
class CodeElement:
    """Represents a code element with semantic information"""
    name: str
    element_type: str     # 'function', 'struct', 'macro', 'variable'
    content: str
    file_path: str
    start_line: int
    end_line: int
    documentation: Optional[str] = None
    dependencies: Optional[List[str]] = None
    usage_examples: Optional[List[str]] = None
    configuration_deps: Optional[List[str]] = None
    semantic_tags: Optional[Set[str]] = None

class SemanticChunker:
    """Advanced semantic chunking system"""
    
    def __init__(self):
        self.relationships: List[SemanticRelationship] = []
        self.code_elements: Dict[str, CodeElement] = {}
        self.dependency_graph: Dict[str, Set[str]] = {}
        
    def analyze_codebase_semantics(self, file_contents: Dict[str, str]) -> Dict[str, Any]:
        """Analyze semantic relationships across the entire codebase"""
        print("🧠 [SEMANTIC] Analyzing codebase semantics...")
        
        # Step 1: Extract all code elements
        for file_path, content in file_contents.items():
            self._extract_code_elements(file_path, content)
        
        # Step 2: Build dependency relationships
        self._build_dependency_graph()
        
        # Step 3: Identify semantic clusters
        clusters = self._identify_semantic_clusters()
        
        # Step 4: Create enhanced chunks
        enhanced_chunks = self._create_semantic_chunks(clusters)
        
        return {
            "elements": self.code_elements,
            "relationships": self.relationships,
            "clusters": clusters,
            "enhanced_chunks": enhanced_chunks
        }
    
    def _extract_code_elements(self, file_path: str, content: str) -> None:
        """Extract code elements with semantic information"""
        
        # Extract functions with documentation
        functions = self._extract_functions_with_docs(file_path, content)
        for func in functions:
            self.code_elements[func.name] = func
        
        # Extract data structures
        structures = self._extract_data_structures(file_path, content)
        for struct in structures:
            self.code_elements[struct.name] = struct
        
        # Extract macros and constants
        macros = self._extract_macros_and_constants(file_path, content)
        for macro in macros:
            self.code_elements[macro.name] = macro
    
    def _extract_functions_with_docs(self, file_path: str, content: str) -> List[CodeElement]:
        """Extract functions with their documentation comments"""
        functions = []
        
        # Pattern to match C/C++ function definitions with preceding comments
        func_pattern = r'(?:/\*\*(.*?)\*/\s*)?(?:static\s+)?(?:inline\s+)?(\w+(?:\s*\*)*)\s+(\w+)\s*\([^)]*\)\s*{'
        
        for match in re.finditer(func_pattern, content, re.DOTALL | re.MULTILINE):
            doc_comment = match.group(1)
            return_type = match.group(2)
            func_name = match.group(3)
            
            # Find function body
            start_pos = match.start()
            func_content, end_pos = self._extract_function_body(content, match.end() - 1)
            
            # Calculate line numbers
            start_line = content[:start_pos].count('\n') + 1
            end_line = content[:end_pos].count('\n') + 1
            
            # Extract dependencies from function body
            dependencies = self._extract_function_dependencies(func_content)
            
            # Create semantic tags
            semantic_tags = self._generate_semantic_tags(func_name, func_content, doc_comment, return_type)
            
            function = CodeElement(
                name=func_name,
                element_type='function',
                content=func_content,
                file_path=file_path,
                start_line=start_line,
                end_line=end_line,
                documentation=doc_comment.strip() if doc_comment else None,
                dependencies=dependencies,
                semantic_tags=semantic_tags
            )
            
            functions.append(function)
        
        return functions
    
    def _extract_data_structures(self, file_path: str, content: str) -> List[CodeElement]:
        """Extract struct/typedef definitions"""
        structures = []
        
        # Pattern for struct definitions
        struct_pattern = r'(?:/\*\*(.*?)\*/\s*)?typedef\s+struct\s+(\w+)?\s*{([^}]+)}\s*(\w+);'
        
        for match in re.finditer(struct_pattern, content, re.DOTALL):
            doc_comment = match.group(1)
            struct_name = match.group(4)  # typedef name
            struct_body = match.group(3)
            
            start_line = content[:match.start()].count('\n') + 1
            end_line = content[:match.end()].count('\n') + 1
            
            # Extract field dependencies
            dependencies = self._extract_struct_dependencies(struct_body)
            
            structure = CodeElement(
                name=struct_name,
                element_type='struct',
                content=match.group(0),
                file_path=file_path,
                start_line=start_line,
                end_line=end_line,
                documentation=doc_comment.strip() if doc_comment else None,
                dependencies=dependencies,
                semantic_tags={'data_structure', 'type_definition'}
            )
            
            structures.append(structure)
        
        return structures
    
    def _extract_macros_and_constants(self, file_path: str, content: str) -> List[CodeElement]:
        """Extract #define macros and constants"""
        macros = []
        
        # Pattern for #define macros
        define_pattern = r'#define\s+(\w+)(?:\([^)]*\))?\s+([^\n\\]*(?:\\\n[^\n\\]*)*)'
        
        for match in re.finditer(define_pattern, content, re.MULTILINE):
            macro_name = match.group(1)
            macro_value = match.group(2).strip()
            
            start_line = content[:match.start()].count('\n') + 1
            end_line = content[:match.end()].count('\n') + 1
            
            # Determine if it's a configuration macro
            is_config = any(keyword in macro_name for keyword in ['CNFG', 'CONFIG', 'MAX', 'MIN', 'ENABLE', 'DISABLE'])
            
            semantic_tags = {'macro'}
            if is_config:
                semantic_tags.add('configuration')
            
            macro = CodeElement(
                name=macro_name,
                element_type='macro',
                content=match.group(0),
                file_path=file_path,
                start_line=start_line,
                end_line=end_line,
                documentation=macro_value if macro_value else None,
                semantic_tags=semantic_tags
            )
            
            macros.append(macro)
        
        return macros
    
    def _build_dependency_graph(self) -> None:
        """Build dependency relationships between code elements"""
        print("🔗 [SEMANTIC] Building dependency graph...")
        
        for element_name, element in self.code_elements.items():
            self.dependency_graph[element_name] = set()
            
            if element.dependencies:
                for dep in element.dependencies:
                    if dep in self.code_elements:
                        self.dependency_graph[element_name].add(dep)
                        
                        # Create relationship
                        relationship = SemanticRelationship(
                            source_element=element_name,
                            target_element=dep,
                            relationship_type='uses',
                            strength=0.8,
                            context=f"{element_name} uses {dep}"
                        )
                        self.relationships.append(relationship)
    
    def _identify_semantic_clusters(self) -> List[Dict[str, Any]]:
        """Identify clusters of related code elements"""
        print("🎯 [SEMANTIC] Identifying semantic clusters...")
        
        clusters: List[Dict[str, Any]] = []
        visited: set[str] = set()
        
        for element_name in self.code_elements:
            if element_name in visited:
                continue
            
            # Find all related elements using DFS
            cluster_elements = self._find_related_elements(element_name, visited)
            
            if len(cluster_elements) > 1:  # Only create clusters with multiple elements
                cluster = {
                    'name': f"cluster_{len(clusters) + 1}",
                    'primary_element': element_name,
                    'elements': cluster_elements,
                    'cluster_type': self._determine_cluster_type(cluster_elements)
                }
                clusters.append(cluster)
        
        return clusters
    
    def _find_related_elements(self, start_element: str, visited: set) -> List[str]:
        """Find all elements related to the start element using DFS"""
        related = []
        stack = [start_element]
        local_visited = set()
        
        while stack:
            current = stack.pop()
            if current in local_visited:
                continue
            
            local_visited.add(current)
            visited.add(current)
            related.append(current)
            
            # Add dependencies
            if current in self.dependency_graph:
                for dep in self.dependency_graph[current]:
                    if dep not in local_visited:
                        stack.append(dep)
            
            # Add reverse dependencies (elements that depend on current)
            for element, deps in self.dependency_graph.items():
                if current in deps and element not in local_visited:
                    stack.append(element)
        
        return related
    
    def _create_semantic_chunks(self, clusters: List[Dict[str, Any]]) -> List[Dict[str, Any]]:
        """Create enhanced chunks based on semantic clusters"""
        print("📦 [SEMANTIC] Creating semantic chunks...")
        
        enhanced_chunks = []
        
        for cluster in clusters:
            chunk = self._create_cluster_chunk(cluster)
            enhanced_chunks.append(chunk)
        
        # Create individual chunks for unclustered elements
        clustered_elements = set()
        for cluster in clusters:
            clustered_elements.update(cluster['elements'])
        
        for element_name, element in self.code_elements.items():
            if element_name not in clustered_elements:
                chunk = self._create_individual_chunk(element)
                enhanced_chunks.append(chunk)
        
        return enhanced_chunks
    
    def _create_cluster_chunk(self, cluster: Dict[str, Any]) -> Dict[str, Any]:
        """Create a chunk from a semantic cluster"""
        elements = [self.code_elements[name] for name in cluster['elements']]
        
        # Sort elements by importance (functions first, then structs, then macros)
        elements.sort(key=lambda e: {'function': 0, 'struct': 1, 'macro': 2}.get(e.element_type, 3))
        
        # Build multi-level content
        content_parts = []
        
        # Level 1: Primary function with documentation
        primary_element = self.code_elements[cluster['primary_element']]
        if primary_element.documentation:
            content_parts.append(f"/* {primary_element.documentation} */")
        content_parts.append(primary_element.content)
        
        # Level 2: Related data structures
        for element in elements:
            if element.element_type == 'struct' and element.name != cluster['primary_element']:
                content_parts.append(f"\n// Related structure: {element.name}")
                content_parts.append(element.content)
        
        # Level 3: Configuration macros
        for element in elements:
            if (element.element_type == 'macro' and
                element.semantic_tags and 'configuration' in element.semantic_tags):
                content_parts.append(f"\n// Configuration: {element.name}")
                content_parts.append(element.content)
        
        # Level 4: Usage examples (other functions in cluster)
        for element in elements:
            if (element.element_type == 'function' and 
                element.name != cluster['primary_element'] and
                len(elements) <= 5):  # Only include if cluster is small
                content_parts.append(f"\n// Related function: {element.name}")
                content_parts.append(element.content)
        
        return {
            'content': '\n\n'.join(content_parts),
            'metadata': {
                'chunk_type': 'semantic_cluster',
                'cluster_name': cluster['name'],
                'primary_element': cluster['primary_element'],
                'element_count': len(elements),
                'cluster_type': cluster['cluster_type'],
                'semantic_tags': list(set().union(*[e.semantic_tags or set() for e in elements])),
                'file_paths': list(set(e.file_path for e in elements)),
                'context_level': 'multi_level'
            }
        }
    
    def _create_individual_chunk(self, element: CodeElement) -> Dict[str, Any]:
        """Create a chunk for an individual element"""
        content_parts = []
        
        if element.documentation:
            content_parts.append(f"/* {element.documentation} */")
        
        content_parts.append(element.content)
        
        return {
            'content': '\n\n'.join(content_parts),
            'metadata': {
                'chunk_type': 'individual_element',
                'element_name': element.name,
                'element_type': element.element_type,
                'semantic_tags': list(element.semantic_tags or set()),
                'file_path': element.file_path,
                'context_level': 'signature'
            }
        }
    
    # Helper methods
    def _extract_function_body(self, content: str, start_pos: int) -> Tuple[str, int]:
        """Extract complete function body including braces"""
        brace_count = 0
        pos = start_pos
        
        while pos < len(content):
            if content[pos] == '{':
                brace_count += 1
            elif content[pos] == '}':
                brace_count -= 1
                if brace_count == 0:
                    return content[start_pos:pos + 1], pos + 1
            pos += 1
        
        return content[start_pos:], len(content)
    
    def _extract_function_dependencies(self, func_content: str) -> List[str]:
        """Extract function calls and variable references"""
        dependencies = []
        
        # Find function calls
        call_pattern = r'(\w+)\s*\('
        for match in re.finditer(call_pattern, func_content):
            func_name = match.group(1)
            if func_name not in ['if', 'while', 'for', 'switch', 'return']:
                dependencies.append(func_name)
        
        # Find macro usage
        macro_pattern = r'([A-Z_][A-Z0-9_]*)'
        for match in re.finditer(macro_pattern, func_content):
            macro_name = match.group(1)
            dependencies.append(macro_name)
        
        return list(set(dependencies))  # Remove duplicates
    
    def _extract_struct_dependencies(self, struct_body: str) -> List[str]:
        """Extract type dependencies from struct definition"""
        dependencies = []
        
        # Find type references
        type_pattern = r'(\w+)\s+\w+;'
        for match in re.finditer(type_pattern, struct_body):
            type_name = match.group(1)
            if type_name not in ['int', 'char', 'float', 'double', 'void']:
                dependencies.append(type_name)
        
        return dependencies
    
    def _generate_semantic_tags(self, func_name: str, func_content: str, doc_comment: Optional[str], return_type: Optional[str] = None) -> Set[str]:
        """Generate semantic tags for a function"""
        tags = {'function'}

        # Analyze function name
        name_lower = func_name.lower()
        if any(keyword in name_lower for keyword in ['alloc', 'malloc', 'free']):
            tags.add('memory_management')
        if any(keyword in name_lower for keyword in ['init', 'initialize']):
            tags.add('initialization')
        if any(keyword in name_lower for keyword in ['close', 'cleanup', 'destroy']):
            tags.add('cleanup')

        # Analyze return type
        if return_type:
            return_type_clean = return_type.strip()
            if return_type_clean == 'void':
                tags.add('procedure')
            elif '*' in return_type_clean:
                tags.add('returns_pointer')
                if any(keyword in name_lower for keyword in ['create', 'new', 'alloc']):
                    tags.add('constructor_like')
            elif return_type_clean in ['int', 'bool', 'BOOL']:
                tags.add('returns_status')
            elif return_type_clean in ['char*', 'const char*', 'string']:
                tags.add('returns_string')

        # Analyze content
        if 'LOCK' in func_content:
            tags.add('thread_safe')
        if '#if' in func_content:
            tags.add('conditional_compilation')

        # Analyze documentation
        if doc_comment:
            doc_lower = doc_comment.lower()
            if 'public' in doc_lower or 'api' in doc_lower:
                tags.add('public_api')
            if 'internal' in doc_lower or 'private' in doc_lower:
                tags.add('internal')

        return tags
    
    def _determine_cluster_type(self, cluster_elements: List[str]) -> str:
        """Determine the type of a semantic cluster"""
        element_types = [self.code_elements[name].element_type for name in cluster_elements]
        
        if 'function' in element_types and 'struct' in element_types:
            return 'functional_module'
        elif element_types.count('function') > 1:
            return 'function_family'
        elif 'struct' in element_types and 'macro' in element_types:
            return 'data_definition'
        else:
            return 'mixed'

# Usage example and integration point
def integrate_semantic_chunking(existing_chunker):
    """Integration function to enhance existing chunker with semantic capabilities"""
    
    def enhanced_chunk_file(file_path: str, content: str, language: str) -> List[Dict[str, Any]]:
        """Enhanced chunking with semantic analysis"""
        
        # Get basic chunks from existing system
        basic_chunks = existing_chunker.chunk_file(file_path, content, language)
        
        # Apply semantic enhancement for C/C++ files
        if language in ['c_cpp', 'c', 'cpp']:
            semantic_chunker = SemanticChunker()
            
            # Analyze just this file for now (could be extended to full codebase)
            semantic_analysis = semantic_chunker.analyze_codebase_semantics({file_path: content})

            # Enhance chunks with semantic information
            enhanced_chunks = []
            for chunk in basic_chunks:
                enhanced_chunk = chunk.copy()

                # Add semantic metadata
                if 'metadata' not in enhanced_chunk:
                    enhanced_chunk['metadata'] = {}

                # Use actual semantic analysis results
                enhanced_chunk['metadata']['semantic_analysis'] = True
                enhanced_chunk['metadata']['context_level'] = 'enhanced'
                enhanced_chunk['metadata']['semantic_elements'] = semantic_analysis.get('elements', {})
                enhanced_chunk['metadata']['semantic_relationships'] = semantic_analysis.get('relationships', [])
                enhanced_chunk['metadata']['semantic_clusters'] = semantic_analysis.get('clusters', [])

                # Try to match chunk content with semantic elements for more specific enhancement
                chunk_content = chunk.get('content', '')
                relevant_elements = {}
                for element_name, element_data in semantic_analysis.get('elements', {}).items():
                    if element_name in chunk_content:
                        relevant_elements[element_name] = element_data

                if relevant_elements:
                    enhanced_chunk['metadata']['relevant_semantic_elements'] = relevant_elements

                enhanced_chunks.append(enhanced_chunk)
            
            return enhanced_chunks
        
        return basic_chunks
    
    return enhanced_chunk_file

class EnhancedQueryProcessor:
    """Enhanced query processing with dependency analysis and context expansion"""

    def __init__(self, semantic_chunker: SemanticChunker):
        self.semantic_chunker = semantic_chunker
        self.query_patterns = {
            'function_explanation': r'(?:explain|describe|what\s+(?:does|is))\s+(\w+)',
            'function_usage': r'(?:how\s+to\s+use|usage\s+of|example\s+of)\s+(\w+)',
            'dependency_query': r'(?:what\s+uses|dependencies\s+of|related\s+to)\s+(\w+)',
            'configuration_query': r'(?:config|configuration|settings?\s+for)\s+(\w+)'
        }

    def enhance_query(self, query: str, codebase_elements: Dict[str, CodeElement]) -> Dict[str, Any]:
        """Enhance query with semantic context and dependency analysis"""

        # Detect query type and extract target element
        query_type, target_element = self._detect_query_type(query)

        if not target_element or target_element not in codebase_elements:
            return {'enhanced_query': query, 'context_expansion': []}

        element = codebase_elements[target_element]
        context_expansion = []

        # Build context based on query type
        if query_type == 'function_explanation':
            context_expansion = self._build_explanation_context(element, codebase_elements)
        elif query_type == 'function_usage':
            context_expansion = self._build_usage_context(element, codebase_elements)
        elif query_type == 'dependency_query':
            context_expansion = self._build_dependency_context(element, codebase_elements)
        elif query_type == 'configuration_query':
            context_expansion = self._build_configuration_context(element, codebase_elements)

        # Create enhanced query
        enhanced_query = self._create_enhanced_query(query, target_element, context_expansion)

        return {
            'original_query': query,
            'enhanced_query': enhanced_query,
            'target_element': target_element,
            'query_type': query_type,
            'context_expansion': context_expansion,
            'search_terms': self._extract_search_terms(context_expansion)
        }

    def _detect_query_type(self, query: str) -> Tuple[str, Optional[str]]:
        """Detect query type and extract target element"""
        query_lower = query.lower()

        for pattern_name, pattern in self.query_patterns.items():
            match = re.search(pattern, query_lower)
            if match:
                return pattern_name, match.group(1)

        # Fallback: look for function names in query
        words = query.split()
        for word in words:
            if word in self.semantic_chunker.code_elements:
                return 'general', word

        return 'general', None

    def _build_explanation_context(self, element: CodeElement, codebase_elements: Dict[str, CodeElement]) -> List[str]:
        """Build context for explaining a function"""
        context = []

        # Add function signature and documentation
        context.append(f"function_signature:{element.name}")
        if element.documentation:
            context.append(f"documentation:{element.name}")

        # Add data structures used
        if element.dependencies:
            for dep in element.dependencies:
                if dep in codebase_elements and codebase_elements[dep].element_type == 'struct':
                    context.append(f"data_structure:{dep}")

        # Add configuration macros
        if element.dependencies:
            for dep in element.dependencies:
                dep_element = codebase_elements.get(dep)
                if (dep_element and
                    dep_element.element_type == 'macro' and
                    dep_element.semantic_tags is not None and
                    'configuration' in dep_element.semantic_tags):
                    context.append(f"configuration:{dep}")

        return context

    def _build_usage_context(self, element: CodeElement, codebase_elements: Dict[str, CodeElement]) -> List[str]:
        """Build context for showing usage examples"""
        context = []

        # Find functions that call this function
        for name, other_element in codebase_elements.items():
            if (other_element.dependencies and
                element.name in other_element.dependencies and
                other_element.element_type == 'function'):
                context.append(f"usage_example:{name}")

        # Add related functions (same prefix/family)
        element_prefix = element.name.split('_')[0] if '_' in element.name else element.name[:4]
        for name, other_element in codebase_elements.items():
            if (name.startswith(element_prefix) and
                name != element.name and
                other_element.element_type == 'function'):
                context.append(f"related_function:{name}")

        return context

    def _build_dependency_context(self, element: CodeElement, codebase_elements: Dict[str, CodeElement]) -> List[str]:
        """Build context for dependency analysis"""
        context = []

        # Direct dependencies
        if element.dependencies:
            for dep in element.dependencies:
                if dep in codebase_elements:
                    context.append(f"direct_dependency:{dep}")

        # Reverse dependencies (what uses this element)
        for name, other_element in codebase_elements.items():
            if (other_element.dependencies and element.name in other_element.dependencies):
                context.append(f"used_by:{name}")

        return context

    def _build_configuration_context(self, element: CodeElement, codebase_elements: Dict[str, CodeElement]) -> List[str]:
        """Build context for configuration-related queries"""
        context = []

        # Find configuration macros that affect this element
        if element.dependencies:
            for dep in element.dependencies:
                dep_element = codebase_elements.get(dep)
                if (dep_element and
                    dep_element.element_type == 'macro' and
                    dep_element.semantic_tags is not None and
                    'configuration' in dep_element.semantic_tags):
                    context.append(f"config_macro:{dep}")

        # Find conditional compilation blocks
        if '#if' in element.content:
            # Extract conditional compilation macros
            ifdef_pattern = r'#if(?:def|ndef)?\s+([A-Z_][A-Z0-9_]*)'
            for match in re.finditer(ifdef_pattern, element.content):
                macro_name = match.group(1)
                context.append(f"conditional_macro:{macro_name}")

        return context

    def _create_enhanced_query(self, original_query: str, target_element: str, context_expansion: List[str]) -> str:
        """Create enhanced query with context terms"""
        enhanced_terms = [original_query]

        # Add context terms
        for context_item in context_expansion:
            context_type, context_value = context_item.split(':', 1)
            enhanced_terms.append(context_value)

        return ' '.join(enhanced_terms)

    def _extract_search_terms(self, context_expansion: List[str]) -> List[str]:
        """Extract search terms from context expansion"""
        search_terms = []

        for context_item in context_expansion:
            context_type, context_value = context_item.split(':', 1)
            search_terms.append(context_value)

        return search_terms

class ContextAwareRanker:
    """Context-aware ranking system that prioritizes documentation and configuration"""

    def __init__(self):
        self.ranking_weights = {
            'has_documentation': 2.0,
            'is_public_api': 1.8,
            'has_configuration': 1.5,
            'is_primary_function': 1.3,
            'has_usage_examples': 1.2,
            'semantic_cluster': 1.1
        }

    def rank_search_results(self, results: List[Dict[str, Any]], query_context: Dict[str, Any]) -> List[Dict[str, Any]]:
        """Rank search results based on context and semantic information"""

        scored_results = []

        for result in results:
            score = self._calculate_result_score(result, query_context)
            scored_results.append({
                'result': result,
                'score': score,
                'ranking_factors': self._get_ranking_factors(result)
            })

        # Sort by score (highest first)
        scored_results.sort(key=lambda x: cast(float, x['score']), reverse=True)

        return [cast(Dict[str, Any], item['result']) for item in scored_results]

    def _calculate_result_score(self, result: Dict[str, Any], query_context: Dict[str, Any]) -> float:
        """Calculate relevance score for a search result"""
        base_score = 1.0
        metadata = result.get('metadata', {})

        # Documentation bonus
        if 'documentation' in result.get('content', ''):
            base_score *= self.ranking_weights['has_documentation']

        # Public API bonus
        if 'public_api' in metadata.get('semantic_tags', []):
            base_score *= self.ranking_weights['is_public_api']

        # Configuration relevance
        if 'configuration' in metadata.get('semantic_tags', []):
            base_score *= self.ranking_weights['has_configuration']

        # Primary function in cluster
        if metadata.get('chunk_type') == 'semantic_cluster':
            base_score *= self.ranking_weights['semantic_cluster']

        # Query type specific bonuses
        query_type = query_context.get('query_type', 'general')
        if query_type == 'function_explanation' and 'function_signature' in result.get('content', ''):
            base_score *= 1.5
        elif query_type == 'configuration_query' and 'config' in result.get('content', '').lower():
            base_score *= 1.8

        return base_score

    def _get_ranking_factors(self, result: Dict[str, Any]) -> List[str]:
        """Get list of factors that influenced ranking"""
        factors = []
        content = result.get('content', '')
        metadata = result.get('metadata', {})

        if 'documentation' in content:
            factors.append('has_documentation')
        if 'public_api' in metadata.get('semantic_tags', []):
            factors.append('is_public_api')
        if 'configuration' in metadata.get('semantic_tags', []):
            factors.append('has_configuration')
        if metadata.get('chunk_type') == 'semantic_cluster':
            factors.append('semantic_cluster')

        return factors

if __name__ == "__main__":
    # Example usage
    chunker = SemanticChunker()

    # Test with sample C code
    sample_code = """
    /* function: tmwmem_lowFree
     * purpose:  Low level deallocation routine
     */
    void TMWDEFS_GLOBAL tmwmem_lowFree(
      TMWMEM_POOL_STRUCT *pAllocStruct,
      TMWMEM_HEADER *pHeader)
    {
      if(pAllocStruct->allocated == 0U) return;
      TMWTARG_LOCK_SECTION(&_memoryPool.lock);
      tmwtarg_free(pHeader);
      TMWTARG_UNLOCK_SECTION(&_memoryPool.lock);
    }
    """

    analysis = chunker.analyze_codebase_semantics({"test.c": sample_code})
    print(f"Found {len(analysis['elements'])} code elements")
    print(f"Created {len(analysis['enhanced_chunks'])} enhanced chunks")

    # Test query enhancement
    query_processor = EnhancedQueryProcessor(chunker)
    enhanced = query_processor.enhance_query("explain tmwmem_lowFree function", analysis['elements'])
    print(f"Enhanced query: {enhanced['enhanced_query']}")
    print(f"Context expansion: {enhanced['context_expansion']}")
